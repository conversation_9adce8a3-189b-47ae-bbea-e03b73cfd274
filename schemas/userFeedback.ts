const feedbackCategories = ['general', 'feature_request', 'bug_report', 'user_experience', 'recipe_quality', 'other'];

const baseFeedbackProperties = {
  feedback: {
    type: 'string',
    description: 'The user feedback about the app, features, or experience',
  },
  category: {
    type: 'string',
    enum: feedbackCategories,
    description: 'Category of the feedback',
  },
};

/**
 * JSON schema for single user feedback extraction
 * Used when extracting a single feedback from user input.
 */
export const singleUserFeedbackSchema = {
  type: 'object',
  properties: baseFeedbackProperties,
  required: ['feedback', 'category'],
  additionalProperties: false,
};

/**
 * JSON schema for user feedback collection
 * Used when storing multiple feedbacks from a single user in Firestore.
 */
export const userFeedbackSchema = {
  type: 'object',
  properties: {
    feedbacks: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          ...baseFeedbackProperties,
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'When the feedback was provided',
          },
        },
        required: ['feedback', 'category', 'timestamp'],
        additionalProperties: false,
      },
      description: 'Array of user feedbacks',
    },
  },
  required: ['feedbacks'],
  additionalProperties: false,
};
