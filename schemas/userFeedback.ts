/**
 * JSON schema for single user feedback extraction
 *
 * This schema is used when extracting a single feedback from user input.
 */
export const singleUserFeedbackSchema = {
  type: 'object',
  properties: {
    feedback: {
      type: 'string',
      description: 'The user feedback about the app, features, or experience',
    },
    category: {
      type: 'string',
      enum: ['general', 'feature_request', 'bug_report', 'user_experience', 'recipe_quality', 'other'],
      description: 'Category of the feedback',
    },
  },
  required: ['feedback', 'category'],
  additionalProperties: false,
};

/**
 * JSON schema for user feedback collection
 *
 * This schema defines the expected structure when storing multiple
 * feedbacks from a single user in Firestore.
 */
export const userFeedbackSchema = {
  type: 'object',
  properties: {
    feedbacks: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          feedback: {
            type: 'string',
            description: 'The user feedback about the app, features, or experience',
          },
          category: {
            type: 'string',
            enum: ['general', 'feature_request', 'bug_report', 'user_experience', 'recipe_quality', 'other'],
            description: 'Category of the feedback',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'When the feedback was provided',
          },
        },
        required: ['feedback', 'category', 'timestamp'],
        additionalProperties: false,
      },
      description: 'Array of user feedbacks',
    },
  },
  required: ['feedbacks'],
  additionalProperties: false,
};
