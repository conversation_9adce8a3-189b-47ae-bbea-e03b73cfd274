/**
 * JSON schema for user feedback
 * 
 * This schema defines the expected structure when users provide
 * feedback about the app, its features, or their experience.
 */
export const userFeedbackSchema = {
  type: 'object',
  properties: {
    feedback: {
      type: 'string',
      description: 'The user feedback about the app, features, or experience'
    },
    category: {
      type: 'string',
      enum: ['general', 'feature_request', 'bug_report', 'user_experience', 'recipe_quality', 'other'],
      description: 'Category of the feedback'
    }
  },
  required: ['feedback', 'category'],
  additionalProperties: false,
};
