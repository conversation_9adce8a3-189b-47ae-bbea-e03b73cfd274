import { useEffect, useState } from 'react';
import { ChatMessage, Roles, DietPreferences, UserFeedback } from '@/constants/Types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { LlmService } from '@/services/LlmService';
import { chatCompletionModel } from '@/constants/LlmConfigs';
import { useAuth } from '@/contexts/AuthContext';
import { partialDietPreferencesSchema } from '@/schemas/partialDietPreferences';
import { userFeedbackSchema } from '@/schemas/userFeedback';

const defaultInstruction = `You are a helpful cooking assistant. Give meal ideas, nutrition tips, and help users to update their saved preferences.

IMPORTANT INSTRUCTIONS FOR SPECIAL CASES:

1. DIET PREFERENCE MODIFICATIONS:
When any of what the users say is related to their dietary preferences (allergies, diet type, cooking time, experience level, calorie goals, personal health, or dietary goals), you should:
- Identify ONLY the specific fields that need to be changed based on the user's request
- Map changes to the appropriate fields: allergies (array), diet (string), timeToCook (string), experience (beginner/intermediate/experienced), calories (number), goals (string)
- If it's a goal related change, verify if it's a new goal or a change/addition to an existing goal. If it's an addition, make sure to include the existing goal(s) in the response as well.
- If any changes cannot be mapped to existing fields, put them in the notes field
- Respond with ONLY the fields that need to be updated wrapped in [SAVE_DIET_PREFERENCES] tags
- DO NOT include unchanged fields in the response
- Format examples:
  * If user wants to change diet only: [SAVE_DIET_PREFERENCES]{"diet": "keto"}[/SAVE_DIET_PREFERENCES]
  * If user adds allergies: [SAVE_DIET_PREFERENCES]{"allergies": ["peanuts", "shellfish"]}[/SAVE_DIET_PREFERENCES]
  * If user changes multiple fields: [SAVE_DIET_PREFERENCES]{"diet": "vegan", "calories": 2000}[/SAVE_DIET_PREFERENCES]
- Then provide a helpful response acknowledging the changes

2. USER FEEDBACK DETECTION:
If a user is providing feedback about the app, its features, user experience, recipe quality, bugs, or suggestions, you should:
- Extract the feedback and categorize it appropriately
- Respond with a JSON object wrapped in [SAVE_FEEDBACK] tags
- Format: [SAVE_FEEDBACK]{"feedback": "user's feedback", "category": "general|feature_request|bug_report|user_experience|recipe_quality|other"}[/SAVE_FEEDBACK]
- Then thank them for their feedback

Examples of feedback indicators:
- "The app is great but..."
- "I wish the app could..."
- "There's a bug when..."
- "The recipes are too complicated"
- "I love how the app..."
- "It would be better if..."

Keep your responses helpful, friendly, and focused on cooking, recipes, and nutrition. Always prioritize the user's cooking and dietary needs.`;

const defaultIntro: ChatMessage = {
  role: Roles.developer,
  content: `Would you like to update any dietary preference? I can also chat about meal ideas and nutrition tips. Here are your current preference settings:`,
};

export function useChatExperience() {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState('');
  const [conversation, setConversation] = useState<ChatMessage[]>([defaultIntro]);
  const [context, setContext] = useState<ResponseInputItem[]>([]);
  const [isTyping, setIsTyping] = useState(true);

  // Fetch diet preferences when user is available
  useEffect(() => {
    if (user) {
      fetchAndShowDietPreferences(user.uid);
    }
  }, [user]);

  useEffect(() => {
    if (conversation.length) {
      storeConversation(conversation);
    }
  }, [conversation]);

  const fetchAndShowDietPreferences = async (userId: string) => {
    try {
      const doc = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, userId);
      if (doc) {
        const preferences = doc as DietPreferences;
        const dietMessage = {
          role: Roles.developer,
          content: `User's dietary preferences are: ${JSON.stringify(preferences)}.`,
        };

        const summaryResponse = await LlmService.callLlmApi(
          chatCompletionModel,
          "Respond ONLY with a bullet points summary of the user's dietary preferences.",
          [dietMessage]
        );

        const outputText = LlmService.extractOutputText(summaryResponse);
        setIsTyping(false);
        setConversation((prev) => [
          ...prev,
          { role: Roles.assistant, content: outputText },
          {
            role: Roles.assistant,
            content:
              'By the way, if you have any feedback for the app, just let me know and I will pass it to the development team.',
          },
        ]);
      }
    } catch (error) {
      console.error('Error fetching diet preferences:', error);
    }
  };

  const storeConversation = async (conv: ChatMessage[]) => {
    if (!user?.uid) return;
    await firestoreRepository.addOrReplaceDocument(FirestoreCollections.CONVERSATIONS, user.uid, {
      conversation: conv,
    });
  };

  const handleSend = async () => {
    if (!messageInput.trim()) return;

    setTimeout(() => setIsTyping(true), 450);

    const userMessage = { role: Roles.user, content: messageInput };
    setConversation((prev) => [...prev, userMessage]);
    const input = messageInput;
    setMessageInput('');

    const apiInput: ResponseInputItem = { role: Roles.user, content: input };

    let outputText;
    try {
      const response = await LlmService.callLlmApi(chatCompletionModel, defaultInstruction, [...context, apiInput]);
      outputText = LlmService.extractOutputText(response);
    } catch (err) {
      console.error('LLM API error:', err);
      setIsTyping(false);
      return;
    }

    // Check for diet preference modifications
    if (outputText.includes('[SAVE_DIET_PREFERENCES]')) {
      await handleDietPreferenceModification(outputText, input);
      outputText = outputText.replace(/\[SAVE_DIET_PREFERENCES\].*?\[\/SAVE_DIET_PREFERENCES\]/s, '').trim();
      if (!outputText) {
        outputText = "I've updated your dietary preferences with the changes you mentioned.";
      }
    }

    // Check for user feedback
    if (outputText.includes('[SAVE_FEEDBACK]')) {
      await handleUserFeedback(outputText, input);
      outputText = outputText.replace(/\[SAVE_FEEDBACK\].*?\[\/SAVE_FEEDBACK\]/s, '').trim();
      if (!outputText) {
        outputText = "Thank you for your feedback! I've recorded your comments to help improve the app.";
      }
    }

    const assistantMessage = { role: Roles.assistant, content: outputText };
    setContext((prev) => [...prev, apiInput, assistantMessage]);
    setConversation((prev) => [...prev, assistantMessage]);
    setIsTyping(false);
  };

  const handleDietPreferenceModification = async (outputText: string, userInput: string) => {
    if (!user?.uid) return;

    try {
      // Get current preferences first
      const currentPrefs = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, user.uid);
      if (!currentPrefs) return;

      // Extract the JSON from the output text
      const jsonMatch = outputText.match(/\[SAVE_DIET_PREFERENCES\](.*?)\[\/SAVE_DIET_PREFERENCES\]/s);
      if (!jsonMatch) {
        // Fallback: use LLM to extract and map modifications from user input
        const contextMessage = {
          role: Roles.developer,
          content: `Current user preferences: ${JSON.stringify(currentPrefs)}. User wants to modify: "${userInput}". Extract ONLY the fields that need to be changed. Do not include unchanged fields. If any changes cannot be mapped to the existing fields (allergies, diet, timeToCook, experience, calories, goals), put them in the notes field.`,
        };

        const response = await LlmService.callLlmApi(
          chatCompletionModel,
          "Extract only the dietary preference fields that need to be updated based on the user's request. Respond with a partial preferences object containing only the changed fields.",
          [contextMessage],
          0.1,
          partialDietPreferencesSchema,
          'preference_changes'
        );

        const extractedText = LlmService.extractOutputText(response);
        const changes: Partial<DietPreferences> = JSON.parse(extractedText);

        // Merge changes with existing preferences
        const updatedPrefs = {
          ...currentPrefs,
          ...changes,
        };

        await firestoreRepository.addOrReplaceDocument(FirestoreCollections.DIET_PREFERENCES, user.uid, updatedPrefs);
      } else {
        // Parse the JSON from the LLM response
        const changes: Partial<DietPreferences> = JSON.parse(jsonMatch[1].trim());

        // Merge changes with existing preferences
        const updatedPrefs = {
          ...currentPrefs,
          ...changes,
        };

        await firestoreRepository.addOrReplaceDocument(FirestoreCollections.DIET_PREFERENCES, user.uid, updatedPrefs);
      }
    } catch (error) {
      console.error('Error handling diet preference modification:', error);
    }
  };

  const handleUserFeedback = async (outputText: string, userInput: string) => {
    if (!user?.uid) return;

    try {
      // Extract the JSON from the output text
      const jsonMatch = outputText.match(/\[SAVE_FEEDBACK\](.*?)\[\/SAVE_FEEDBACK\]/s);
      if (!jsonMatch) {
        // Fallback: use LLM to extract feedback from user input
        const response = await LlmService.callLlmApi(
          chatCompletionModel,
          'Extract the user feedback and categorize it. Respond with a JSON object.',
          [{ role: Roles.user, content: userInput }],
          0.1,
          userFeedbackSchema,
          'user_feedback'
        );

        const extractedText = LlmService.extractOutputText(response);
        const feedback: UserFeedback = JSON.parse(extractedText);

        // Save feedback to Firestore
        const feedbackWithTimestamp = {
          ...feedback,
          timestamp: new Date(),
        };
        await firestoreRepository.addOrReplaceDocument(
          FirestoreCollections.USER_FEEDBACKS,
          user.uid,
          feedbackWithTimestamp
        );
      } else {
        // Parse the JSON from the LLM response
        const feedback: UserFeedback = JSON.parse(jsonMatch[1].trim());

        // Save feedback to Firestore
        const feedbackWithTimestamp = {
          ...feedback,
          timestamp: new Date(),
        };
        await firestoreRepository.addOrReplaceDocument(
          FirestoreCollections.USER_FEEDBACKS,
          user.uid,
          feedbackWithTimestamp
        );
      }
    } catch (error) {
      console.error('Error handling user feedback:', error);
    }
  };

  return {
    user,
    messageInput,
    setMessageInput,
    conversation,
    isTyping,
    handleSend,
  };
}
