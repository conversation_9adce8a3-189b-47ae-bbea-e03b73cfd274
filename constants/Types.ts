export enum Roles {
  user = 'user',
  assistant = 'assistant',
  developer = 'developer',
}

export type ChatMessage = {
  role: Roles;
  content: string;
};

export type DietPreferences = {
  allergies: string[];
  diet: string;
  timeToCook: string;
  experience: ExperienceLevel;
  calories: number;
  goals: string;
  notes?: string; // Optional field for storing user modifications
};

export type UserFeedback = {
  feedback: string;
  category: 'general' | 'feature_request' | 'bug_report' | 'user_experience' | 'recipe_quality' | 'other';
  timestamp: Date;
};

export type UserFeedbacks = {
  feedbacks: UserFeedback[];
};

export enum ExperienceLevel {
  beginner = 'beginner',
  intermediate = 'intermediate',
  experienced = 'experienced',
}
